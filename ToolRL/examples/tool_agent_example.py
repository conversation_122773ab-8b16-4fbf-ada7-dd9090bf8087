import torch
import argparse
from transformers import AutoTokenizer
from verl.data_proto import DataProto
from verl.workers.rollout.vllm_rollout.vllm_rollout import vLLMRollout
from verl.agents.tool_agent import ToolAgent

def parse_args():
    parser = argparse.ArgumentParser(description="运行工具代理示例")
    parser.add_argument("--model", type=str, required=True, help="模型路径或名称")
    parser.add_argument("--max_turns", type=int, default=5, help="最大交互轮次")
    parser.add_argument("--search_url", type=str, default="http://localhost:8000/search", help="搜索服务URL")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 加载tokenizer
    print(f"加载tokenizer: {args.model}")
    tokenizer = AutoTokenizer.from_pretrained(args.model)
    tokenizer.pad_token = tokenizer.eos_token
    
    # 初始化rollout
    print("初始化vLLMRollout...")
    rollout = vLLMRollout(
        model_name=args.model,
        tokenizer=tokenizer,
        tensor_parallel_size=1,  # 根据需要调整
    )
    
    # 配置类
    class Config:
        def __init__(self):
            self.max_turns = args.max_turns
            self.search_url = args.search_url
    
    config = Config()
    
    # 初始化工具代理
    print("初始化ToolAgent...")
    tool_agent = ToolAgent(rollout, tokenizer, config)
    
    # 示例提示
    prompts = [
        "请帮我查询关于人工智能的最新进展。",
        "什么是量子计算？"
    ]
    
    print("编码提示...")
    encoded = tokenizer(prompts, return_tensors='pt', padding=True)
    input_ids = encoded['input_ids']
    attention_mask = encoded['attention_mask']
    position_ids = torch.arange(input_ids.shape[1]).unsqueeze(0).repeat(len(prompts), 1)
    
    # 构造DataProto
    prompts_proto = DataProto(batch={
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'position_ids': position_ids
    })
    
    # 运行交互循环
    print("运行交互循环...")
    results = tool_agent.run_interaction_loop(prompts_proto)
    
    # 处理结果
    print("\n===== 结果 =====")
    for i, context in enumerate(results.batch['full_contexts']):
        print(f"\n----- 示例 {i+1} -----")
        print(context)

if __name__ == "__main__":
    main()