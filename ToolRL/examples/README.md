# ToolRL 示例

本目录包含ToolRL项目的示例脚本。

## 工具代理示例

`tool_agent_example.py`展示了如何使用ToolAgent进行多轮交互和工具调用。

### 使用方法

1. 确保搜索服务已启动（参考Search-R1项目的`retrieval_launch.sh`）

2. 运行示例脚本：

```bash
python examples/tool_agent_example.py --model <your-model-path> --max_turns 5
```

参数说明：
- `--model`: 模型路径或名称
- `--max_turns`: 最大交互轮次（默认为5）

注意：搜索服务URL在SearchClient中硬编码，无需配置。

### 输出示例

脚本将输出每个示例的完整交互历史，包括：
- 初始提示
- 模型生成的内容
- 工具调用和结果
- 最终响应