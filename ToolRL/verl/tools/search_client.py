"""
改进的搜索客户端模块，增加了token限制处理功能。
"""

import requests
import json
import logging
from typing import List, Dict, Any, Optional, Union
import tiktoken  # 需要安装: pip install tiktoken
import os

# === 硬编码检索服务器URL ===
SEARCH_SERVER_URL = "http://ggpu173:8000/retrieve"

# 配置日志
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SearchClient:
    """
    搜索客户端类，用于与检索服务器通信，支持token限制。

    属性:
        server_url: 检索服务器的URL（只用文件内硬编码的SEARCH_SERVER_URL）
        timeout: 请求超时时间（秒）
        max_tokens: 最大token限制
        tokenizer: token计数器
    """

    def __init__(self, search_url: str = None, timeout: int = 30, max_retries: int = 3, max_tokens: int = 4000, encoding_name: str = "cl100k_base"):
        """
        初始化搜索客户端。

        参数:
            search_url: 搜索服务器URL，如果为None则使用默认的SEARCH_SERVER_URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            max_tokens: 最大token限制，默认4000
            encoding_name: tokenizer编码名称，默认为cl100k_base（适用于GPT-4）
        """
        self.server_url = search_url if search_url is not None else SEARCH_SERVER_URL
        self.timeout = timeout
        self.max_retries = max_retries
        self.max_tokens = max_tokens
        # 初始化tokenizer
        try:
            self.tokenizer = tiktoken.get_encoding(encoding_name)
        except Exception as e:
            logger.warning(f"无法初始化tokenizer: {e}。如需精确token计数，请先 pip install tiktoken 并确保编码名称正确。将使用简单字符计数，结果可能不精确。")
            self.tokenizer = None
        # 创建带有重试机制的会话
        self.session = self._create_session()
        logger.info(f"搜索客户端初始化完成，最大token限制: {self.max_tokens}，服务器URL: {self.server_url}")

    def _create_session(self) -> requests.Session:
        """
        创建带有重试机制的会话。
        
        返回:
            requests.Session: 配置好的会话对象
        """
        session = requests.Session()
        retry_strategy = requests.adapters.Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )
        adapter = requests.adapters.HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def count_tokens(self, text: str) -> int:
        """
        计算文本的token数量。

        参数:
            text: 要计算的文本

        返回:
            int: token数量
        """
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            logger.warning("未安装tiktoken或初始化失败，token计数为字符估算，结果可能不精确。建议安装tiktoken。");
            return len(text) // 4

    def truncate_text(self, text: str, max_tokens: int) -> str:
        """
        截断文本以适应token限制。

        参数:
            text: 要截断的文本
            max_tokens: 最大token数量

        返回:
            str: 截断后的文本
        """
        if self.count_tokens(text) <= max_tokens:
            return text
        if self.tokenizer:
            tokens = self.tokenizer.encode(text)
            truncated_tokens = tokens[:max_tokens]
            return self.tokenizer.decode(truncated_tokens)
        else:
            estimated_chars = max_tokens * 4
            return text[:estimated_chars]

    def _filter_search_results_with_token_limit(self, search_results: Dict[str, Any], top_k: int) -> List[Dict[str, Any]]:
        """
        过滤和处理搜索结果，考虑token限制。
        
        参数:
            search_results: 原始搜索结果
            top_k: 最大结果数量
            
        返回:
            List[Dict[str, Any]]: 过滤后的结果
        """
        # 如果top_k小于等于0，直接返回空列表
        if top_k <= 0:
            logger.info("top_k <= 0，返回空结果列表")
            return []
            
        filtered_results = []
        current_tokens = 0
        
        # 为每个结果分配的最大token数（保留一些空间给其他内容）
        max_tokens_per_result = min(500, self.max_tokens // max(top_k, 1))
        
        # 处理answer_box
        answer_box = search_results.get('answer_box', {})
        if isinstance(answer_box, dict) and answer_box and current_tokens < self.max_tokens:
            title = answer_box.get('title', 'No title.') if isinstance(answer_box.get('title', None), str) else 'No title.'
            snippet = answer_box.get('snippet', 'No snippet available.') if isinstance(answer_box.get('snippet', None), str) else 'No snippet available.'
            content = f'"{title}"\n{snippet}'
            truncated_content = self.truncate_text(content, max_tokens_per_result)
            result_tokens = self.count_tokens(truncated_content)
            if current_tokens + result_tokens <= self.max_tokens:
                filtered_results.append({
                    'document': {"contents": truncated_content},
                    'token_count': result_tokens
                })
                current_tokens += result_tokens

        # 处理organic_results
        organic_results = search_results.get('organic_results', [])
        if not isinstance(organic_results, list):
            organic_results = []
        for result in organic_results:
            if len(filtered_results) >= top_k or current_tokens >= self.max_tokens:
                break
            title = result.get('title', 'No title.') if isinstance(result.get('title', None), str) else 'No title.'
            snippet = result.get('snippet', 'No snippet available.') if isinstance(result.get('snippet', None), str) else 'No snippet available.'
            content = f'"{title}"\n{snippet}'
            truncated_content = self.truncate_text(content, max_tokens_per_result)
            result_tokens = self.count_tokens(truncated_content)
            if current_tokens + result_tokens <= self.max_tokens:
                filtered_results.append({
                    'document': {"contents": truncated_content},
                    'token_count': result_tokens
                })
                current_tokens += result_tokens

        # 处理related_questions
        related_results = search_results.get('related_questions', [])
        if not isinstance(related_results, list):
            related_results = []
        for result in related_results:
            if len(filtered_results) >= top_k or current_tokens >= self.max_tokens:
                break
            title = result.get('question', 'No title.') if isinstance(result.get('question', None), str) else 'No title.'
            snippet = result.get('snippet', 'No snippet available.') if isinstance(result.get('snippet', None), str) else 'No snippet available.'
            content = f'"{title}"\n{snippet}'
            truncated_content = self.truncate_text(content, max_tokens_per_result)
            result_tokens = self.count_tokens(truncated_content)
            if current_tokens + result_tokens <= self.max_tokens:
                filtered_results.append({
                    'document': {"contents": truncated_content},
                    'token_count': result_tokens
                })
                current_tokens += result_tokens

        logger.debug(f"过滤后返回 {len(filtered_results)} 条结果，总token数: {current_tokens}")
        return filtered_results

    def search(self, query: str, top_k: int = 5, max_tokens: Optional[int] = None) -> str:
        """
        发送搜索查询到检索服务器，返回所有 document['contents'] 拼接后的字符串（带序号），可直接放入 obs 的 results 字段。
        """
        result = self._execute_search(query, top_k=top_k)
        hits = []
        if isinstance(result, dict) and 'result' in result and isinstance(result['result'], list) and len(result['result']) > 0:
            hits = result['result'][0]
        contents_list = []
        for i, hit in enumerate(hits):
            doc = hit.get('document', {})
            content = doc.get('contents', '')
            if content:
                contents_list.append(f"{i+1}. {content.strip()}")
        results_text = "\n\n".join(contents_list)
        return results_text

    def _execute_search(self, query: str, top_k: int = 1) -> Dict[str, Any]:
        """执行搜索查询"""
        try:
            payload = {
                "queries": [query],
                "topk": top_k,
                "return_scores": True
            }

            logger.info(f"发送搜索请求到: {self.server_url}")
            logger.debug(f"请求负载: {payload}")

            response = self.session.post(
                self.server_url,
                json=payload,
                timeout=self.timeout
            )

            logger.info(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                results = response.json()
                logger.debug(f"搜索成功，响应: {results}")
                # 直接返回原始结果，不做格式转换
                return results
            else:
                logger.warning(f"搜索请求失败，状态码: {response.status_code}")
                logger.warning(f"响应内容: {response.text}")
                return self._mock_search(query, top_k)

        except requests.exceptions.RequestException as e:
            logger.warning(f"搜索请求异常: {e}")
            return self._mock_search(query, top_k)

    def _mock_search(self, query: str, top_k: int = 3) -> Dict[str, Any]:
        """模拟搜索"""
        logger.warning(f"使用模拟搜索结果，查询: {query}")
        mock_results = []

        for i in range(min(top_k, 3)):
            mock_results.append({
                "document": {
                    "id": f"mock_{i}",
                    "contents": f"模拟搜索结果 {i+1} 对于查询: {query}"
                },
                "score": 1.0 - (i * 0.1)
            })

        return {
            "query": query,
            "results": mock_results
        }

    def batch_search(self, queries: List[str], top_k: int = 3, max_tokens: Optional[int] = None) -> List[str]:
        """
        批量搜索，返回字符串列表，每个字符串为对应搜索结果所有 document['contents'] 拼接后的内容（带序号）。
        """
        results = []
        for query in queries:
            result = self._execute_search(query, top_k=top_k)
            hits = []
            if isinstance(result, dict) and 'result' in result and isinstance(result['result'], list) and len(result['result']) > 0:
                hits = result['result'][0]
            contents_list = []
            for i, hit in enumerate(hits):
                doc = hit.get('document', {})
                content = doc.get('contents', '')
                if content:
                    contents_list.append(f"{i+1}. {content.strip()}")
            results_text = "\n\n".join(contents_list)
            results.append(results_text)
        return results

    def get_token_stats(self) -> Dict[str, Any]:
        """
        获取token使用统计信息。
        
        返回:
            Dict[str, Any]: 包含token相关配置和统计信息
        """
        return {
            "max_tokens": self.max_tokens,
            "tokenizer_available": self.tokenizer is not None,
            "encoding_name": "cl100k_base" if self.tokenizer else "simple_estimation"
        }

    @staticmethod
    def format_obs_from_search_result(search_result: dict) -> str:
        """
        将搜索服务器返回的结果格式化为 <obs> 结构字符串。
        只提取每条 document 的 contents 字段，多条用两个换行拼接。
        返回形如：<obs> {"name": "search", "results": "内容1\n\n内容2..."} </obs>
        """
        # 兼容不同返回结构
        hits = []
        if isinstance(search_result, dict):
            if 'result' in search_result and isinstance(search_result['result'], list) and len(search_result['result']) > 0:
                hits = search_result['result'][0]
        if not hits:
            return '<obs> {"name": "search", "results": ""} </obs>'
        # 提取内容
        contents_list = []
        for hit in hits:
            doc = hit.get('document', {})
            content = doc.get('contents', '')
            if content:
                contents_list.append(content.strip())
        results_text = "\n\n".join(contents_list)
        obs_str = f'<obs> {{"name": "search", "results": "{results_text}"}} </obs>'
        return obs_str


# 创建一个默认的搜索客户端实例
default_client = SearchClient()

def search(query: str, top_k: int = 5, max_tokens: Optional[int] = None, 
          client: Optional[SearchClient] = None) -> Dict[str, Any]:
    """
    使用搜索客户端发送搜索查询的便捷函数，支持token限制。

    参数:
        query: 搜索查询
        top_k: 返回的结果数量，默认为5
        max_tokens: 最大token限制
        client: 搜索客户端实例，默认使用default_client

    返回:
        Dict[str, Any]: 搜索结果，包含token统计信息
    """
    if client is None:
        client = default_client

    return client.search(query, top_k, max_tokens)
