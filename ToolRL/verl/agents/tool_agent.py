import re
import json
import torch
import requests
from typing import List, Dict, Any, Tuple, Optional
from verl import DataProto
from verl.protocol import pad_dataproto_to_divisor, unpad_dataproto
from verl.tools.search_client import SearchClient

class ToolAgent:
    """工具增强的LLM代理，负责多轮交互逻辑，参考Search-R1的LLMGenerationManager设计"""

    def __init__(self, rollout, tokenizer, config=None):
        """
        初始化工具代理

        Args:
            rollout: 用于生成文本的rollout对象
            tokenizer: 分词器
            config: 配置对象
        """
        self.rollout = rollout
        self.tokenizer = tokenizer
        self.config = config or {}

        # 初始化搜索客户端（使用硬编码URL）
        self.search_client = SearchClient()

        # 获取配置参数
        if hasattr(config, 'trainer') and hasattr(config.trainer, 'max_turns'):
            self.max_turns = config.trainer.max_turns
        else:
            self.max_turns = getattr(config, "max_turns", 5)

        # 搜索相关配置
        self.search_url = "http://ggpu173:8000/retrieve"  # 硬编码搜索URL
        self.topk = 3
        
    def extract_tool_query(self, text):
        """提取工具调用查询"""
        match = re.search(r'<tool_call>(.*?)</tool_call>', text, re.DOTALL)
        if match:
            tool_text = match.group(1).strip()
            try:
                tool_json = json.loads(tool_text)
                if tool_json.get("name") == "search":
                    return tool_json["parameters"]["query"]
            except:
                return tool_text
        return ""

    def extract_response(self, text):
        """提取响应内容"""
        match = re.search(r'<response>(.*?)</response>', text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return text.strip()

    def postprocess_predictions(self, predictions: List[str]) -> Tuple[List[str], List[str]]:
        """
        处理模型预测输出，检查是否包含tool_call或response

        Args:
            predictions: 模型生成的文本列表

        Returns:
            Tuple of (actions list, contents list)
        """
        actions = []
        contents = []

        for prediction in predictions:
            if "<tool_call>" in prediction:
                actions.append("search")
                contents.append(self.extract_tool_query(prediction))
            elif "<response>" in prediction:
                actions.append("answer")
                contents.append(self.extract_response(prediction))
            else:
                actions.append(None)
                contents.append("")

        return actions, contents

    def batch_search(self, queries: List[str]) -> List[str]:
        """
        批量搜索查询

        Args:
            queries: 搜索查询列表

        Returns:
            搜索结果列表
        """
        if not queries:
            return []

        payload = {
            "queries": queries,
            "topk": self.topk,
            "return_scores": True
        }

        try:
            response = requests.post(self.search_url, json=payload)
            results = response.json()['result']
            return [self._passages2string(result) for result in results]
        except Exception as e:
            print(f"Search failed: {e}")
            return [f"Search failed for query: {query}" for query in queries]

    def _passages2string(self, retrieval_result):
        """将检索结果格式化为字符串"""
        format_reference = ''
        for idx, doc_item in enumerate(retrieval_result):
            content = doc_item['document']['contents']
            title = content.split("\n")[0]
            text = "\n".join(content.split("\n")[1:])
            format_reference += f"Doc {idx+1}(Title: {title}) {text}\n"
        return format_reference
    
    def run_interaction_loop(self, gen_batch: DataProto) -> DataProto:
        """
        运行多轮交互循环，参考Search-R1的LLMGenerationManager.run_llm_loop实现

        Args:
            gen_batch: 输入的DataProto对象

        Returns:
            包含完整交互历史的DataProto对象
        """
        device = gen_batch.batch['input_ids'].device
        batch_size = gen_batch.batch['input_ids'].shape[0]

        # 初始化状态
        active_mask = torch.ones(batch_size, dtype=torch.bool)
        rollings = gen_batch

        # 存储原始prompt和累积的responses
        original_prompts = gen_batch.batch['input_ids'].clone()
        accumulated_responses = torch.empty(batch_size, 0, dtype=torch.long, device=device)

        # 主交互循环
        for step in range(self.max_turns):
            if not active_mask.sum():
                break

            # 只处理活跃的样本
            rollings_active = DataProto.from_single_dict({
                k: v[active_mask] for k, v in rollings.batch.items()
            })

            # 添加padding以支持多worker分块
            rollings_padded, pad_size = pad_dataproto_to_divisor(rollings_active, self.rollout.world_size)
            gen_output_padded = self.rollout.generate_sequences(rollings_padded)
            # 移除padding
            gen_output = unpad_dataproto(gen_output_padded, pad_size=pad_size)

            # 后处理生成的响应
            responses_ids, responses_str = self._postprocess_responses(gen_output.batch['responses'])

            # 将responses扩展到完整batch大小
            responses_ids = self._expand_to_full_batch(responses_ids, active_mask, batch_size, device)
            responses_str = self._expand_to_full_batch_str(responses_str, active_mask, batch_size)

            # 执行环境交互（搜索等）
            next_obs, dones, valid_actions, is_search = self.execute_predictions(
                responses_str, active_mask
            )

            # 更新活跃状态
            curr_active_mask = torch.tensor([not done for done in dones], dtype=torch.bool)
            active_mask = active_mask & curr_active_mask

            # 处理观察结果
            next_obs_ids = self._process_next_obs(next_obs, device)

            # 更新累积的responses
            accumulated_responses = self._concatenate_responses(
                accumulated_responses, responses_ids, next_obs_ids
            )

            # 更新rolling状态
            rollings = self._update_rolling_state(rollings, responses_ids, next_obs_ids)

        # 最终生成（如果还有活跃样本）
        if active_mask.sum():
            rollings_active = DataProto.from_single_dict({
                k: v[active_mask] for k, v in rollings.batch.items()
            })

            rollings_padded, pad_size = pad_dataproto_to_divisor(rollings_active, self.rollout.world_size)
            gen_output_padded = self.rollout.generate_sequences(rollings_padded)
            gen_output = unpad_dataproto(gen_output_padded, pad_size=pad_size)

            responses_ids, responses_str = self._postprocess_responses(gen_output.batch['responses'])
            responses_ids = self._expand_to_full_batch(responses_ids, active_mask, batch_size, device)

            # 最终更新
            accumulated_responses = self._concatenate_responses(
                accumulated_responses, responses_ids, None
            )

        # 构造最终输出
        return self._compose_final_output(original_prompts, accumulated_responses, gen_batch)

    def _postprocess_responses(self, responses: torch.Tensor) -> Tuple[torch.Tensor, List[str]]:
        """后处理响应，截断到tool_call或response标签"""
        responses_str = self.tokenizer.batch_decode(responses, skip_special_tokens=True)

        # 截断到第一个</tool_call>或</response>
        processed_str = []
        for resp in responses_str:
            if '</tool_call>' in resp:
                resp = resp.split('</tool_call>')[0] + '</tool_call>'
            elif '</response>' in resp:
                resp = resp.split('</response>')[0] + '</response>'
            processed_str.append(resp)

        # 重新编码
        processed_ids = self.tokenizer(
            processed_str,
            add_special_tokens=False,
            return_tensors='pt',
            padding="longest"
        )['input_ids']

        return processed_ids, processed_str

    def _expand_to_full_batch(self, tensor: torch.Tensor, active_mask: torch.Tensor,
                             batch_size: int, device: torch.device) -> torch.Tensor:
        """将活跃样本的tensor扩展到完整batch大小"""
        if tensor.shape[0] == batch_size:
            return tensor

        # 检查tensor大小是否与活跃样本数量匹配
        active_count = active_mask.sum().item()
        if tensor.shape[0] != active_count:
            print(f"Warning: tensor size {tensor.shape[0]} != active count {active_count}")
            # 如果不匹配，截断或填充tensor
            if tensor.shape[0] > active_count:
                tensor = tensor[:active_count]
            else:
                # 如果tensor太小，用最后一个样本填充
                padding_size = active_count - tensor.shape[0]
                if padding_size > 0:
                    last_sample = tensor[-1:].repeat(padding_size, 1)
                    tensor = torch.cat([tensor, last_sample], dim=0)

        full_tensor = torch.zeros(batch_size, tensor.shape[1], dtype=tensor.dtype, device=device)
        full_tensor[active_mask] = tensor
        return full_tensor

    def _expand_to_full_batch_str(self, str_list: List[str], active_mask: torch.Tensor,
                                 batch_size: int) -> List[str]:
        """将活跃样本的字符串列表扩展到完整batch大小"""
        if len(str_list) == batch_size:
            return str_list

        # 检查字符串列表大小是否与活跃样本数量匹配
        active_count = active_mask.sum().item()
        if len(str_list) != active_count:
            print(f"Warning: str_list size {len(str_list)} != active count {active_count}")
            # 如果不匹配，截断或填充
            if len(str_list) > active_count:
                str_list = str_list[:active_count]
            else:
                # 如果列表太小，用空字符串填充
                str_list.extend([""] * (active_count - len(str_list)))

        full_list = [""] * batch_size
        active_indices = active_mask.nonzero(as_tuple=False).squeeze(-1).tolist()
        for i, idx in enumerate(active_indices):
            if i < len(str_list):
                full_list[idx] = str_list[i]
        return full_list

    def _process_next_obs(self, next_obs: List[str], device: torch.device) -> torch.Tensor:
        """处理下一步观察结果"""
        if not any(next_obs):  # 如果所有观察都为空
            return torch.empty(len(next_obs), 0, dtype=torch.long, device=device)

        next_obs_ids = self.tokenizer(
            next_obs,
            padding='longest',
            return_tensors='pt',
            add_special_tokens=False,
        )['input_ids'].to(device)

        return next_obs_ids

    def _concatenate_responses(self, accumulated: torch.Tensor, responses: torch.Tensor,
                              obs: Optional[torch.Tensor]) -> torch.Tensor:
        """拼接累积的响应"""
        tensors_to_cat = [accumulated, responses]
        if obs is not None and obs.shape[1] > 0:
            tensors_to_cat.append(obs)

        return torch.cat(tensors_to_cat, dim=1)

    def _update_rolling_state(self, rollings: DataProto, responses: torch.Tensor,
                             obs: Optional[torch.Tensor]) -> DataProto:
        """更新rolling状态"""
        # 拼接新的内容
        tensors_to_cat = [rollings.batch['input_ids'], responses]
        if obs is not None and obs.shape[1] > 0:
            tensors_to_cat.append(obs)

        new_input_ids = torch.cat(tensors_to_cat, dim=1)

        # 创建attention mask和position ids
        new_attention_mask = (new_input_ids != self.tokenizer.pad_token_id).long()
        new_position_ids = torch.arange(new_input_ids.shape[1], device=new_input_ids.device).unsqueeze(0).repeat(new_input_ids.shape[0], 1)

        # 更新DataProto
        new_rollings = DataProto.from_single_dict({
            'input_ids': new_input_ids,
            'attention_mask': new_attention_mask,
            'position_ids': new_position_ids
        })
        new_rollings.meta_info.update(rollings.meta_info)

        return new_rollings

    def execute_predictions(self, predictions: List[str], active_mask: torch.Tensor) -> Tuple[List[str], List[bool], List[bool], List[bool]]:
        """
        执行预测结果，处理工具调用和响应

        Returns:
            Tuple of (next_obs, dones, valid_actions, is_search)
        """
        actions, contents = self.postprocess_predictions(predictions)
        next_obs, dones, valid_actions, is_search = [], [], [], []

        # 收集所有搜索查询
        search_queries = [content for action, content in zip(actions, contents) if action == 'search' and content]
        search_results = self.batch_search(search_queries) if search_queries else []
        search_iter = iter(search_results)

        for i, (action, content, active) in enumerate(zip(actions, contents, active_mask)):
            if not active:
                next_obs.append('')
                dones.append(True)
                valid_actions.append(False)
                is_search.append(False)
            elif action == 'answer':
                next_obs.append('')
                dones.append(True)
                valid_actions.append(True)
                is_search.append(False)
            elif action == 'search' and content:
                search_result = next(search_iter, "Search failed")
                # 使用你的obs格式
                next_obs.append(f'<obs>{json.dumps({"name": "search", "results": search_result}, ensure_ascii=False)}</obs>')
                dones.append(False)
                valid_actions.append(True)
                is_search.append(True)
            else:
                # 无效动作或继续生成
                next_obs.append('')
                dones.append(False)
                valid_actions.append(False)
                is_search.append(False)

        return next_obs, dones, valid_actions, is_search

    def _compose_final_output(self, prompts: torch.Tensor, responses: torch.Tensor,
                             original_batch: DataProto) -> DataProto:
        """组合最终输出"""
        # 拼接prompt和response
        input_ids = torch.cat([prompts, responses], dim=1)

        # 创建attention mask
        attention_mask = (input_ids != self.tokenizer.pad_token_id).long()

        # 创建position ids
        position_ids = torch.arange(input_ids.shape[1], device=input_ids.device).unsqueeze(0).repeat(input_ids.shape[0], 1)

        # 构造最终batch
        final_batch = {
            'prompts': prompts,
            'responses': responses,
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'position_ids': position_ids
        }

        final_output = DataProto.from_single_dict(final_batch)
        final_output.meta_info.update(original_batch.meta_info)

        return final_output