import re
import json
import torch
from typing import List, Dict, Any, Tuple, Optional
from verl.data_proto import DataProto
from verl.tools.search_client import SearchClient

class ToolAgent:
    """工具增强的LLM代理，负责多轮交互逻辑"""
    
    def __init__(self, rollout, tokenizer, config=None):
        """
        初始化工具代理

        Args:
            rollout: 用于生成文本的rollout对象
            tokenizer: 分词器
            config: 配置对象
        """
        self.rollout = rollout
        self.tokenizer = tokenizer
        self.config = config or {}

        # 获取搜索配置
        if hasattr(config, 'trainer') and hasattr(config.trainer, 'search_url'):
            search_url = config.trainer.search_url
        else:
            search_url = getattr(config, "search_url", "http://localhost:8000/search")
        self.search_client = SearchClient(search_url=search_url)

        # 获取最大轮次配置
        if hasattr(config, 'trainer') and hasattr(config.trainer, 'max_turns'):
            self.max_turns = config.trainer.max_turns
        else:
            self.max_turns = getattr(config, "max_turns", 5)
        
    def extract_tool_query(self, text):
        """提取工具调用查询"""
        match = re.search(r'<tool_call>(.*?)</tool_call>', text, re.DOTALL)
        if match:
            tool_text = match.group(1).strip()
            try:
                tool_json = json.loads(tool_text)
                if tool_json.get("name") == "search":
                    return tool_json["parameters"]["query"]
            except:
                return tool_text
        return ""
    
    def extract_response(self, text):
        """提取响应内容"""
        match = re.search(r'<response>(.*?)</response>', text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return text.strip()  # 如果没有标记，返回整个文本
    
    def run_interaction_loop(self, prompts: DataProto) -> DataProto:
        """
        运行交互循环，支持单轮和多轮交互
        
        Args:
            prompts: 输入提示的DataProto对象
            
        Returns:
            包含完整交互历史的DataProto对象
        """
        device = prompts.batch['input_ids'].device
        batch_size = prompts.batch['input_ids'].shape[0]
        
        # 初始化交互状态
        contexts = []
        for i in range(batch_size):
            input_ids = prompts.batch['input_ids'][i]
            # 去除左padding
            pad_token_id = self.tokenizer.pad_token_id
            non_pad_index = (input_ids != pad_token_id).nonzero(as_tuple=False)
            if len(non_pad_index) > 0:
                input_ids = input_ids[non_pad_index[0][0]:]
            context = self.tokenizer.decode(input_ids, skip_special_tokens=True)
            contexts.append(context)
        
        done_mask = [False] * batch_size
        final_responses = [""] * batch_size
        
        # 主交互循环
        for step in range(self.max_turns):
            if all(done_mask):
                break
                
            # 只处理未完成的样本
            active_indices = [i for i, done in enumerate(done_mask) if not done]
            active_contexts = [contexts[i] for i in active_indices]
            
            # 编码当前上下文
            encoded = self.tokenizer(active_contexts, return_tensors='pt', padding=True, truncation=True)
            input_ids = encoded['input_ids'].to(device)
            attention_mask = encoded['attention_mask'].to(device)
            position_ids = torch.arange(input_ids.shape[1]).unsqueeze(0).repeat(len(active_indices), 1).to(device)
            
            # 构造DataProto并生成
            active_prompts = DataProto(batch={
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'position_ids': position_ids
            })
            
            # 单步生成
            outputs = self.rollout.generate_sequences(active_prompts)
            output_ids = outputs.batch['responses']
            output_texts = [self.tokenizer.decode(ids, skip_special_tokens=True) for ids in output_ids]
            
            # 处理生成结果
            for idx, output_text in zip(active_indices, output_texts):
                if "<tool_call>" in output_text:
                    # 提取并执行工具调用
                    query = self.extract_tool_query(output_text)
                    if query:
                        search_results = self.search_client.search(query, top_k=3)
                        search_json = {
                            "name": "search",
                            "results": search_results
                        }
                        tool_response = f"<obs>{json.dumps(search_json, ensure_ascii=False)}</obs>"
                        contexts[idx] = contexts[idx] + output_text + tool_response
                    else:
                        contexts[idx] = contexts[idx] + output_text
                elif "<response>" in output_text:
                    # 完成生成
                    contexts[idx] = contexts[idx] + output_text
                    done_mask[idx] = True
                    final_responses[idx] = self.extract_response(output_text)
                else:
                    # 如果是第一轮且没有特殊标记，视为单轮交互
                    if step == 0:
                        contexts[idx] = contexts[idx] + f"<response>{output_text}</response>"
                        done_mask[idx] = True
                        final_responses[idx] = output_text
                    else:
                        # 继续生成
                        contexts[idx] = contexts[idx] + output_text
        
        # 处理未完成的样本（达到最大轮次但仍未完成）
        for i, done in enumerate(done_mask):
            if not done:
                # 强制结束，将当前内容包装为响应
                if "<response>" not in contexts[i]:
                    contexts[i] += "<response>无法完成请求</response>"
                final_responses[i] = self.extract_response(contexts[i])
        
        # 构造最终结果
        final_encoded = self.tokenizer(contexts, return_tensors='pt', padding=True, truncation=True)
        final_input_ids = final_encoded['input_ids'].to(device)
        final_attention_mask = final_encoded['attention_mask'].to(device)
        
        # 编码最终响应
        response_encoded = self.tokenizer(final_responses, return_tensors='pt', padding=True, truncation=True)
        response_ids = response_encoded['input_ids'].to(device)
        
        # 构造返回的DataProto
        result_batch = {
            'prompts': prompts.batch['input_ids'],
            'responses': response_ids,
            'input_ids': final_input_ids,
            'attention_mask': final_attention_mask,
            'full_contexts': contexts  # 保存完整交互历史
        }
        
        return DataProto(batch=result_batch)