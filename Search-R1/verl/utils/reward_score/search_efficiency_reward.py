import os
import numpy as np
from typing import List, Optional, Union, Tuple
import math
import logging
import json
import re
from sentence_transformers import SentenceTransformer, util

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def compute_length_penalty(len_pred: int, len_gt: int, alpha: float = 0.1) -> float:
    if len_gt == 0:
        return 0.0
    ratio = len_pred / len_gt
    if ratio > 1:
        # 更温和一点的线性惩罚
        return max(0.7, 1 - alpha * (ratio - 1))
    return 1.0

def preprocess_answer(answer: str) -> str:
    """
    预处理答案文本
    
    Args:
        answer: 原始答案文本
    
    Returns:
        str: 预处理后的答案文本
    """
    if not answer:
        return ""
    
    # 转换为小写
    answer = answer.lower()
    
    # 移除标点符号，保留字母数字和空格
    answer = re.sub(r'[^\w\s]', ' ', answer)
    
    # 移除多余空格
    answer = ' '.join(answer.split())
    
    return answer.strip()

def compute_lcs(str1: str, str2: str) -> str:
    """
    计算两个字符串的最长公共子序列
    
    Args:
        str1: 第一个字符串
        str2: 第二个字符串
        
    Returns:
        str: 最长公共子序列
    """
    # 将字符串转换为单词列表
    words1 = str1.split()
    words2 = str2.split()
    
    # 创建DP表
    m, n = len(words1), len(words2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    
    # 填充DP表
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if words1[i-1] == words2[j-1]:
                dp[i][j] = dp[i-1][j-1] + 1
            else:
                dp[i][j] = max(dp[i-1][j], dp[i][j-1])
    
    # 回溯得到最长公共子序列
    lcs = []
    i, j = m, n
    while i > 0 and j > 0:
        if words1[i-1] == words2[j-1]:
            lcs.append(words1[i-1])
            i -= 1
            j -= 1
        elif dp[i-1][j] > dp[i][j-1]:
            i -= 1
        else:
            j -= 1
    
    # 反转并连接单词
    return ' '.join(reversed(lcs))

def classify_answer_type(answer: str) -> str:
    answer = answer.strip().lower()
    # 首先检查是否是CommonsenseQA的选择题答案格式
    if re.fullmatch(r'[a-e]', answer):
        return 'multiple_choice'
    elif re.fullmatch(r'(yes|no)', answer):
        return 'yesno'
    elif re.fullmatch(r'[\d,\.%]+', answer):  # 纯数字/比例
        return 'number'
    elif len(answer.split()) <= 3:
        return 'entity'
    else:
        return 'sentence'

def compute_accuracy_reward(model_response: str, gt_responses: List[str], alpha: float = 0.1) -> float:
    gen_norm = preprocess_answer(model_response)
    gen_len = len(gen_norm.split())

    max_score = 0.0
    for ref in gt_responses:
        ref_norm = preprocess_answer(ref)
        ref_len = len(ref_norm.split())
        ref_type = classify_answer_type(ref_norm)

        # ==== 替换原 base_score 逻辑 ====
        if ref_type == "multiple_choice":
            # CommonsenseQA的选择题答案格式
            # 提取模型回答中的选项字母（A-E）
            model_choice = None
            for c in gen_norm:
                if c in 'abcde':
                    model_choice = c
                    break
            # 如果找到了选项且与正确答案匹配
            if model_choice and model_choice == ref_norm:
                base_score = 1.0
            else:
                base_score = 0.0
        
        elif ref_type == "yesno":
            base_score = 1.0 if ref_norm in gen_norm else 0.0

        elif ref_type == "number":
            gold_nums = re.findall(r'\d+', ref_norm)
            pred_nums = re.findall(r'\d+', gen_norm)
            base_score = 1.0 if any(num in pred_nums for num in gold_nums) else 0.0

        elif ref_type == "entity":
            if ref_norm in gen_norm:
                base_score = 1.0
            elif all(w in gen_norm for w in ref_norm.split() if len(w) > 2):
                base_score = 0.8  # 大部分词都命中
            elif any(w in gen_norm for w in ref_norm.split()):
                base_score = 0.5
            else:
                base_score = 0.0

        else:  # sentence or fallback
            if ref_norm in gen_norm:
                base_score = 1.0
                try:
                    position = gen_norm.index(ref_norm)
                    position_words = len(gen_norm[:position].split())
                    position_ratio = position_words / max(1, gen_len)
                    if position_ratio <= 0.2:
                        position_bonus = 1.0
                    elif position_ratio <= 0.5:
                        position_bonus = 0.9
                    else:
                        position_bonus = 0.7
                    base_score *= position_bonus
                except ValueError:
                    pass
            else:
                lcs = compute_lcs(gen_norm, ref_norm)
                if lcs:
                    base_score = len(lcs.split()) / max(1, ref_len)
                else:
                    base_score = 0.0

        len_penalty = compute_length_penalty(gen_len, ref_len, alpha=alpha)
        reward = base_score * len_penalty
        max_score = max(max_score, reward)

    return max_score

def count_searches(solution_str: str) -> int:
    """
    从解决方案字符串中统计搜索次数
    格式示例：
    <think>...</think>
    <tool_call>{"name": "search", "parameters": {"query": "..."}}</tool_call>
    <obs>...</obs>
    """
    # 使用正则表达式匹配 tool_call 标签
    tool_calls = re.findall(r'<tool_call>(.*?)</tool_call>', solution_str)
    
    search_count = 0
    for tool_call in tool_calls:
        try:
            tool_data = json.loads(tool_call)
            if tool_data.get("name") == "search":
                search_count += 1
        except:
            continue
    
    return search_count

def compute_search_efficiency_reward(accuracy_reward: float, num_searches: int) -> float:
    """
    计算搜索效率奖励，遵循以下原则：
    1. 知道就不搜：如果不搜索(num_searches=0)且答对(accuracy高)，给予额外奖励
    2. 不知道才搜：如果答错(accuracy低)且没搜索，严厉惩罚
    3. 搜得少而有效：搜索次数越多，效率奖励越低，但准确率高可以弥补部分损失
    
    Args:
        accuracy_reward: 准确率奖励 [0,1]
        num_searches: 搜索次数
    
    Returns:
        float: 效率奖励 [0,1.2]，其中>1的部分为"知道就不搜"的额外奖励
    """
    # 如果准确率太低，说明回答错误
    if accuracy_reward < 0.3:
        # 如果错误且没搜索，说明应该搜索但没搜
        if num_searches == 0:
            return 0.0  # 严厉惩罚
        else:
            return 0.1  # 给予少量基础分，因为至少尝试了搜索
    
    # 准确率较高的情况
    if accuracy_reward >= 0.7:
        # 不搜索就答对，给予额外奖励
        if num_searches == 0:
            return min(1.2, accuracy_reward +0.2)  # 20%的额外奖励
        # 搜索1次就答对，说明搜索效率高
        elif num_searches == 1:
            return accuracy_reward
        # 搜索2次，轻微惩罚
        elif num_searches == 2:
            return accuracy_reward * 0.9
        # 搜索3次，中度惩罚
        elif num_searches == 3:
            return accuracy_reward * 0.7
        # 搜索超过3次，显著惩罚
        else:
            return accuracy_reward * max(0.3, 0.7 - 0.1 * (num_searches - 3))
    
    # 准确率一般的情况 (0.2 <= accuracy_reward < 0.8)
    else:
        if num_searches == 0:
            # return accuracy_reward * 0.8  # 不搜索且准确率一般，轻微惩罚
            # 当前: accuracy_reward * 0.8
            # 可以考虑更细致的区分
            if 0.3 <= accuracy_reward < 0.5:
                return accuracy_reward * 0.6  # 准确率偏低且不搜索，重点惩罚
            elif 0.5 <= accuracy_reward < 0.7:
                return accuracy_reward * 0.8  # 准确率中等且不搜索，轻微惩罚
        elif num_searches == 1:
            return accuracy_reward * 0.9  # 搜索1次但准确率一般，轻微惩罚
        elif num_searches == 2:
            return accuracy_reward * 0.7  # 搜索2次但准确率一般，中度惩罚
        else:
            return accuracy_reward * max(0.2, 0.7 - 0.15 * (num_searches - 2))  # 搜索多次且准确率一般，显著惩罚

def extract_response_from_text(text: str) -> str:
    """从文本中提取最终回答"""
    pattern = r"<response>(.*?)</response>"
    match = re.search(pattern, text, re.DOTALL)
    return match.group(1).strip() if match else ""

def clean_solution(solution_str):
    match = re.search(r"<\|im_start\|>assistant\n(.*?)<\|im_end\|>", solution_str, re.DOTALL)
    return match.group(1).strip() if match else solution_str.strip()

def compute_format_reward(response_str: str, max_reward: float = 1.0, min_reward: float = 0.0) -> float:
    """
    输入:一个 assistant 的生成字符串 response_str
    要求格式为:
      - 必须以 <think> 开头
      - </response> 必须存在,且是最后一段输出
      - 中间可以有若干轮 <tool_call> → <obs> → <think>,但必须结构闭合
    返回:
      格式完全正确返回 max_reward,否则返回 min_reward
    """
    
    try:
        # 去除首尾空格换行
        response_str = response_str.strip()
        
        # 检查是否以 <think> 开头
        if not response_str.startswith("<think>"):
            return min_reward
        
        # 检查是否以 </response> 结尾
        if not response_str.endswith("</response>"):
            return min_reward
        
        # 检查必须包含 <think> 和 <response> 标签
        if "<think>" not in response_str or "</think>" not in response_str:
            return min_reward
        if "<response>" not in response_str or "</response>" not in response_str:
            return min_reward
        
        # 提取所有标签（开始和结束标签）
        # 修正正则表达式，捕获开始和结束标签
        tag_pattern = r"<(/?)(\w+)>"
        tags = re.findall(tag_pattern, response_str)
        
        # 用栈检查标签是否正确闭合
        stack = []
        valid_tags = {"think", "tool_call", "obs", "response"}
        
        for is_closing, tagname in tags:
            if tagname not in valid_tags:
                return min_reward
            
            if not is_closing:  # 开始标签
                stack.append(tagname)
            else:  # 结束标签
                if not stack or stack[-1] != tagname:
                    return min_reward
                stack.pop()
        
        # 检查栈是否为空（所有标签都已闭合）
        if stack:
            return min_reward
        
        # 检查结构逻辑：
        # 1. 必须以 think 开始，以 response 结束
        # 2. tool_call 后面必须跟 obs
        # 3. obs 后面必须跟 think
        
        # 提取标签序列（只要开始标签）
        start_tags = [tagname for is_closing, tagname in tags if not is_closing]
        
        # 检查开始和结束
        if not start_tags or start_tags[0] != "think":
            return min_reward
        if not start_tags or start_tags[-1] != "response":
            return min_reward
        
        # 检查 tool_call -> obs -> think 的模式
        i = 0
        while i < len(start_tags):
            if start_tags[i] == "tool_call":
                # tool_call 后面必须是 obs
                if i + 1 >= len(start_tags) or start_tags[i + 1] != "obs":
                    return min_reward
                # obs 后面必须是 think 或 response
                if i + 2 >= len(start_tags) or start_tags[i + 2] not in ["think", "response"]:
                    return min_reward
                i += 3  # 跳过 tool_call, obs, think/response
            else:
                i += 1
        
        return max_reward
        
    except Exception as e:
        return min_reward

def compute_score(solution_str: str, ground_truth: str, step: int = 0, max_step: int = 105) -> Tuple[float, float, float]:
    solution_str = clean_solution(solution_str)
    if not solution_str or not ground_truth:
        return 0.0, 0.0, 0.0

    model_response = extract_response_from_text(solution_str)
    gt_response = extract_response_from_text(ground_truth)
    if not model_response or not gt_response:
        return 0.0, 0.0, 0.0

    accuracy_reward = compute_accuracy_reward(model_response, [gt_response])
    num_searches = count_searches(solution_str)
    efficiency_reward = compute_search_efficiency_reward(accuracy_reward, num_searches)
    format_reward = compute_format_reward(solution_str)
    score = efficiency_reward + format_reward
    return score, efficiency_reward, format_reward